import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createI18n } from 'vue-i18n'
import { createPinia, setActivePinia } from 'pinia'
import TheSmartStatusBar from '../TheSmartStatusBar.vue'
import SystemLog from '../SystemLog.vue'

// Mock the composables
vi.mock('@/composables/useTransactionFlowLogic', () => ({
  useTransactionFlowLogic: () => ({
    currentTransaction: { value: { id: 'test-transaction' } },
    timeLeft: { value: 1083 },
    isTimerCritical: { value: false },
    isTimerExpired: { value: false },
    isElapsedTimer: { value: false }
  })
}))

vi.mock('@/composables/useTimerDisplay', () => ({
  useTimerDisplay: () => ({
    timerDisplayValue: { value: '18:03' },
    timerColorClass: { value: 'timer-normal' },
    timerLabel: { value: 'Timer' }
  })
}))

// Mock Naive UI
vi.mock('naive-ui', () => ({
  NIcon: {
    name: 'NIcon',
    template: '<span><slot /></span>'
  },
  useMessage: () => ({
    info: vi.fn(),
    success: vi.fn(),
    warning: vi.fn(),
    error: vi.fn()
  })
}))

// Create i18n instance with our enhanced translations
const i18n = createI18n({
  legacy: false,
  locale: 'en',
  messages: {
    en: {
      transactionalChat: {
        statusBar: {
          stepShort: 'Step {current}',
          clickToScrollToAction: 'Click to scroll to action'
        },
        steps: {
          paymentInfo: 'Provide Payment Information',
          paymentInfoWaiting: 'Waiting for {name} to add payment info',
          paymentInfoShort: 'Add Payment Info',
          paymentInfoWaitingShort: 'Waiting {name}',
          negotiation: 'Decide Who Pays First',
          negotiationWaiting: 'Waiting for {name}\'s decision',
          negotiationShort: 'Decide First Payer',
          negotiationWaitingShort: 'Waiting {name}',
          makePayment: 'Your Action: Send {amount}',
          makePaymentShort: 'Send {amount}',
          waitingPayer1: 'Waiting for {name} to send {amount}',
          waitingPaymentShort: 'Waiting {name}',
          confirmReceipt: 'Your Action: Confirm Receipt of {amount}',
          confirmReceiptShort: 'Confirm Receipt',
          confirmReceiptWaiting: 'Waiting for {name} to confirm receipt',
          confirmReceiptWaitingShort: 'Waiting {name}',
          makeSecondPayment: 'Your Action: Send {amount}',
          makeSecondPaymentShort: 'Send {amount}',
          makeSecondPaymentWaiting: 'Waiting for {name} to send {amount}',
          makeSecondPaymentWaitingShort: 'Waiting {name}',
          confirmFirstPaymentReceipt: 'Your Action: Confirm Final Receipt of {amount}',
          confirmFirstPaymentReceiptShort: 'Confirm Final Receipt',
          confirmFirstPaymentReceiptWaiting: 'Waiting for {name} to confirm final receipt',
          confirmFirstPaymentReceiptWaitingShort: 'Waiting {name}',
          finalized: 'Trade Successful!'
        }
      },
      systemMessages: {
        step: {
          paymentInfoCompleted: 'Payment information has been provided by both parties.',
          negotiationStarted: 'Both parties can now decide who pays first.',
          agreementReached: 'Agreement reached: {firstPayer} will pay first.',
          firstPaymentPhase: 'First payment phase: {firstPayer} should send {amount}.',
          firstPaymentDeclared: '{username} has declared sending {amount}.',
          firstPaymentConfirmed: 'First payment confirmed. Second payment phase begins.',
          secondPaymentPhase: 'Second payment phase: {secondPayer} should send {amount}.',
          secondPaymentDeclared: '{username} has declared sending {amount}.',
          secondPaymentConfirmed: 'Second payment confirmed. Transaction complete!',
          transactionFinalized: '🎉 Transaction successfully completed!'
        },
        payment: {
          declared: '{username} has declared their payment. Waiting for {otherUser} to confirm receipt.',
          confirmedFirst: '{username} has confirmed receipt of the first payment. Now waiting for {payerUser} to make their payment to {receiverUser}. Payment due by {dueDate}.',
          confirmedSecond: '{username} has confirmed receipt of the second payment. Transaction is now complete!'
        },
        transaction: {
          complete: 'Transaction is now complete! 🎉'
        }
      },
      common: {
        you: 'You'
      }
    },
    fa: {
      transactionalChat: {
        statusBar: {
          stepShort: 'مرحله {current}',
          clickToScrollToAction: 'برای رفتن به عمل کلیک کنید'
        },
        steps: {
          paymentInfo: 'ارائه اطلاعات پرداخت',
          paymentInfoWaiting: 'در انتظار افزودن اطلاعات پرداخت توسط {name}',
          paymentInfoShort: 'افزودن اطلاعات پرداخت',
          paymentInfoWaitingShort: 'انتظار {name}',
          negotiation: 'تعیین پرداخت‌کننده اول',
          negotiationWaiting: 'در انتظار تصمیم {name}',
          negotiationShort: 'تعیین پرداخت‌کننده اول',
          negotiationWaitingShort: 'انتظار {name}',
          makePayment: 'اقدام شما: ارسال {amount}',
          makePaymentShort: 'ارسال {amount}',
          waitingPayer1: 'در انتظار ارسال {amount} توسط {name}',
          waitingPaymentShort: 'انتظار {name}',
          confirmReceipt: 'اقدام شما: تأیید دریافت {amount}',
          confirmReceiptShort: 'تأیید دریافت',
          confirmReceiptWaiting: 'در انتظار تأیید دریافت توسط {name}',
          confirmReceiptWaitingShort: 'انتظار {name}',
          makeSecondPayment: 'اقدام شما: ارسال {amount}',
          makeSecondPaymentShort: 'ارسال {amount}',
          makeSecondPaymentWaiting: 'در انتظار ارسال {amount} توسط {name}',
          makeSecondPaymentWaitingShort: 'انتظار {name}',
          confirmFirstPaymentReceipt: 'اقدام شما: تأیید نهایی دریافت {amount}',
          confirmFirstPaymentReceiptShort: 'تأیید نهایی دریافت',
          confirmFirstPaymentReceiptWaiting: 'در انتظار تأیید نهایی توسط {name}',
          confirmFirstPaymentReceiptWaitingShort: 'انتظار {name}',
          finalized: 'معامله موفق!'
        }
      },
      systemMessages: {
        step: {
          transactionFinalized: '🎉 معامله با موفقیت تکمیل شد!'
        },
        payment: {
          declared: '{username} پرداخت خود را اعلام کرده است. منتظر تأیید دریافت از {otherUser}.',
          confirmedSecond: '{username} دریافت پرداخت دوم را تأیید کرده است. معامله اکنون کامل شده است!'
        },
        transaction: {
          complete: 'معامله اکنون کامل شده است! 🎉'
        }
      },
      common: {
        you: 'شما'
      }
    }
  }
})

describe('Enhanced Status Messages', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  describe('Currency Formatting', () => {
    it('should format IRR amounts in millions for large values', () => {
      const mockStore = {
        currentStep: { key: 'makePayment', titleKey: 'transactionalChat.steps.makePayment' },
        currentStepIndex: 2,
        totalSteps: 6,
        timer: { remainingSeconds: 1083 },
        otherUser: { name: 'Test User' },
        transactionDetails: {
          amountToSend: 28500000,
          amountToReceive: 500,
          currencyFrom: 'IRR',
          currencyTo: 'CAD'
        },
        isUsersTurn: true,
        isStatusBarShrunk: false,
        pinnedAction: null,
        isActionCardVisible: false,
        chatSessionId: 'test-session-id'
      }

      vi.doMock('@/stores/transactionalChat/transactionalChatStore', () => ({
        useTransactionalChatStore: () => mockStore
      }))

      vi.doMock('@/stores/auth', () => ({
        useAuthStore: () => ({ user: { id: 'test-user-id' } })
      }))

      vi.doMock('@/stores/payerNegotiation', () => ({
        usePayerNegotiationStore: () => ({ currentNegotiation: null })
      }))

      const wrapper = mount(TheSmartStatusBar, {
        global: {
          plugins: [i18n]
        }
      })

      const statusTitle = wrapper.find('[data-testid="status-title"]')
      expect(statusTitle.text()).toContain('28.5M IRR')
    })

    it('should format non-IRR amounts normally', () => {
      const mockStore = {
        currentStep: { key: 'makePayment', titleKey: 'transactionalChat.steps.makePayment' },
        currentStepIndex: 2,
        totalSteps: 6,
        timer: { remainingSeconds: 1083 },
        otherUser: { name: 'Test User' },
        transactionDetails: {
          amountToSend: 500,
          amountToReceive: 28500000,
          currencyFrom: 'CAD',
          currencyTo: 'IRR'
        },
        isUsersTurn: true,
        isStatusBarShrunk: false,
        pinnedAction: null,
        isActionCardVisible: false,
        chatSessionId: 'test-session-id'
      }

      vi.doMock('@/stores/transactionalChat/transactionalChatStore', () => ({
        useTransactionalChatStore: () => mockStore
      }))

      const wrapper = mount(TheSmartStatusBar, {
        global: {
          plugins: [i18n]
        }
      })

      const statusTitle = wrapper.find('[data-testid="status-title"]')
      expect(statusTitle.text()).toContain('500 CAD')
    })
  })

  describe('SystemLog Message Processing', () => {
    it('should handle translation keys with proper data formatting', () => {
      const wrapper = mount(SystemLog, {
        props: {
          item: {
            id: 'test-1',
            type: 'systemLog',
            message: 'systemMessages.step.transactionFinalized',
            data: {},
            timestamp: new Date().toISOString()
          }
        },
        global: {
          plugins: [i18n]
        }
      })

      expect(wrapper.find('[data-testid="system-message-text"]').text()).toBe('🎉 Transaction successfully completed!')
    })

    it('should detect celebration messages correctly', () => {
      const wrapper = mount(SystemLog, {
        props: {
          item: {
            id: 'test-2',
            type: 'systemLog',
            message: 'systemMessages.transaction.complete',
            data: {},
            timestamp: new Date().toISOString()
          }
        },
        global: {
          plugins: [i18n]
        }
      })

      expect(wrapper.find('.system-log').classes()).toContain('celebration')
    })

    it('should format amounts in system messages', () => {
      const wrapper = mount(SystemLog, {
        props: {
          item: {
            id: 'test-3',
            type: 'systemLog',
            message: 'systemMessages.step.firstPaymentDeclared',
            data: {
              username: 'TestUser',
              amount: 28500000,
              currency: 'IRR'
            },
            timestamp: new Date().toISOString()
          }
        },
        global: {
          plugins: [i18n]
        }
      })

      expect(wrapper.find('[data-testid="system-message-text"]').text()).toContain('28.5M IRR')
    })
  })
})
