{"trackingNumber": "Tracking Number", "notes": "Notes", "header": {"tradingWith": "Trading with {name}", "reputation": "Reputation: {level}", "menu": "<PERSON><PERSON>", "buySummary": "Buy {amount} {currency}", "sellSummary": "Sell {amount} {currency}"}, "steps": {"paymentInfo": "Provide Payment Information", "paymentInfoWaiting": "Waiting for {name} to add payment info", "paymentInfoShort": "Add Payment Info", "paymentInfoWaitingShort": "Waiting {name}", "negotiation": "Decide Who Pays First", "negotiationWaiting": "Waiting for {name}'s decision", "negotiationShort": "Decide First Payer", "negotiationWaitingShort": "Waiting {name}", "waitingPayer1": "Waiting for {name} to send {amount}", "makePayment": "Your Action: Send {amount}", "makePaymentShort": "Send {amount}", "confirmReceipt": "Your Action: Confirm Receipt of {amount}", "confirmReceiptShort": "Confirm Receipt", "confirmReceiptWaiting": "Waiting for {name} to confirm receipt", "confirmReceiptWaitingShort": "Waiting {name}", "yourTurnToPay": "Your Action: Send {amount}", "makeSecondPayment": "Your Action: Send {amount}", "makeSecondPaymentShort": "Send {amount}", "makeSecondPaymentWaiting": "Waiting for {name} to send {amount}", "makeSecondPaymentWaitingShort": "Waiting {name}", "waitingP2": "Waiting for {name} to confirm receipt", "waitingPaymentShort": "Waiting {name}", "waitingConfirmationShort": "Waiting {name}", "confirmFirstPaymentReceipt": "Your Action: Confirm Final Receipt of {amount}", "confirmFirstPaymentReceiptShort": "Confirm Final Receipt", "confirmFirstPaymentReceiptWaiting": "Waiting for {name} to confirm final receipt", "confirmFirstPaymentReceiptWaitingShort": "Waiting {name}", "waitingPayer2": "Waiting for {name} to send {amount}", "waitingPayer2Short": "Waiting {name}", "finalized": "Trade Successful!"}, "statusBar": {"timeLeft": "⏳ Time left: {time}", "step": "Step {current} of {total}", "stepShort": "Step {current}", "clickToScrollToAction": "Click to scroll to action"}, "pinnedBanner": {"viewDetails": "View Details", "subtitles": {"paymentInfo": "Complete this step to continue", "negotiation": "Your decision is required", "confirmReceipt": "Action required - check your account", "confirmFirstPaymentReceipt": "Action required - check your account", "yourTurnToPay": "Send payment using the details below", "makeSecondPayment": "Send payment using the details below", "makePayment": "Send payment using the details below", "rateExperience": "Share your experience", "default": "Action required"}}, "systemLogs": {"tradeStarted": "Trade started.", "paymentDetailsProvided": "Payment details provided.", "readyToNegotiate": "Both parties have provided payment details. Ready to decide who pays first.", "agreementReached": "Agreement reached: {name} will pay first.", "paymentMarkedSent": "{name} has marked {amount} as sent.", "youConfirmedReceipt": "You confirmed receipt.", "youMarkedSent": "You have marked {amount} as sent.", "transactionComplete": "🎉 Transaction Complete!", "smartActionBarDemo": "🎯 DEMO: Notice the bottom bar! This is the 'Smart Dynamic Action Bar' - it shows chat icon (💬) + action button instead of regular chat input.", "singleActionDemo": "🎯 DEMO: Step 5 shows <PERSON><PERSON><PERSON> the action button (no chat icon) as specified in the brief.", "firstPaymentDeclared": "{name} has declared the first payment of {amount}.", "secondPaymentDeclared": "{name} has declared the second payment of {amount}.", "firstPaymentConfirmed": "{name} confirmed receipt of the first payment."}, "actionCards": {"copySuccess": "Copied to clipboard successfully!", "copyError": "Failed to copy to clipboard", "paymentInfo": {"title": "Add Your Payment Information", "description": "Please provide your receiving payment details for this transaction.", "button": "Add Payment Details", "noMethods": "No Payment Methods Saved", "setupForCurrency": "Quick setup for {currency} payments", "addNewMethod": "Add New Payment Method", "selectMethod": "Select Payment Method", "default": "<PERSON><PERSON><PERSON>", "showDetails": "Show Details", "hideDetails": "Hide Details", "bankName": "Bank Name", "accountNumber": "Account Number", "accountHolder": "Account Holder Name", "iban": "IBAN", "swiftCode": "SWIFT Code", "notes": "Notes", "bankNamePlaceholder": "Enter bank name", "accountNumberPlaceholder": "Enter account number", "accountHolderPlaceholder": "Enter account holder name", "ibanPlaceholder": "Enter IBAN (optional)", "swiftPlaceholder": "Enter SWIFT code (optional)", "notesPlaceholder": "Additional notes (optional)", "cancel": "Cancel", "saveMethod": "Save Method", "saveChanges": "Save Changes", "methodAdded": "Payment method added successfully!", "methodUpdated": "Payment method updated successfully!", "methodSelected": "Payment method selected successfully!", "methodSelectFailed": "Failed to select payment method", "methodAddFailed": "Failed to add payment method", "methodUpdateFailed": "Failed to update payment method", "editingNotImplemented": "Inline editing coming soon!", "editMethod": "Edit Method", "methodDetails": "Method Details", "changeMethod": "Change Method", "confirmSelection": "Confirm Selection", "confirmInfo": "Confirm Payment Info", "confirmed": "Payment information confirmed!", "infoConfirmed": "Payment information confirmed successfully!", "confirmFailed": "Failed to confirm payment information", "noMethodsDesc": "Add your first payment method to continue", "addFirstMethod": "Add First Method"}, "negotiation": {"title": "Who Should Pay First?", "whoShouldPayFirst": "Who should pay first?", "loadingDetails": "Loading negotiation details...", "otherShouldPayFirst": "{name} should pay first", "why": "Why?", "iAgree": "I Agree", "waitingForOtherResponse": "Waiting for {name} to respond...", "systemRecommendation": "System Recommendation", "reasonLabel": "Reason", "you": "You", "yourDecision": "Your Decision", "systemSuggests": "System suggests {name} pays first", "waitingForResponse": "Waiting for their response...", "optional": "optional", "sendCounterOffer": "Send Counter Offer", "sendFinalOffer": "Send Final Offer", "loadingNegotiation": "Loading Negotiation...", "loadingDescription": "Fetching negotiation data from server", "paymentSequence": "Payment Sequence", "proposedSequence": "Proposed Payment Sequence", "firstPayment": "First Payment", "secondPayment": "Second Payment", "firstPayer": "Pays First", "firstReceiver": "Receives First", "secondPayer": "Pays Second", "secondReceiver": "Receives Second", "recommendationAccepted": "Recommendation accepted successfully!", "proposalAccepted": "Proposal accepted successfully!", "counterProposed": "Counter offer proposed successfully!", "actionFailed": "Action failed. Please try again.", "acceptButton": "I Accept", "agreeButton": "I Agree", "acceptProposal": "Accept Proposal", "proposeAlternative": "Propose Alternative", "proposeAlternativeDescription": "Suggest a different payment sequence or add a message to explain your reasoning.", "decline": "Decline", "requestButton": "Request Other Pays First", "aiRecommendation": "AI Recommendation", "youPayFirst": "You should pay first", "otherPaysFirst": "{name} should pay first", "chooseOption": "Choose your preferred option:", "iPay": "I'll pay first", "otherPays": "Ask {name} to pay first", "recommended": "Recommended", "youSend": "You send: {amount}", "youReceive": "You receive: {amount}", "theySend": "{name} sends: {amount}", "theyReceive": "{name} receives: {amount}", "advancedOptions": "Advanced Options", "customMessage": "Custom Message (Optional)", "messagePlaceholder": "Add a negotiation message...", "messageHint": "Optional message to explain your choice", "riskAssessment": "Risk Assessment", "yourRisk": "Your Risk", "theirRisk": "{name}'s Risk", "submitDecision": "Submit Decision", "decisionSubmitted": "Your decision has been submitted", "decisionFailed": "Failed to submit decision", "proposalReceived": "{name} has made a proposal", "proposalText": "should pay first", "finalOfferWarning": "Final Offer Warning", "finalOfferDescription": "This will be the final proposal. If the other party declines, the transaction will be cancelled. We recommend discussing this in the chat first before proceeding.", "discussInChatFirst": "Discuss in Chat First", "proceedWithFinal": "Proceed with Final Offer", "discussInChat": "Please discuss this in the chat first", "waitingForDecision": "Waiting for Decision", "waitingMessage": "Waiting for {name} to respond to your proposal", "yourProposal": "Your Proposal", "shouldPayFirst": "should pay first", "agreementReached": "Agreement Reached!", "youWillPayFirst": "You will pay first", "otherWillPayFirst": "{name} will pay first", "proceedingToPayment": "The transaction will now proceed to the payment phase", "preparingPaymentPhase": "Preparing payment phase...", "negotiationCancelled": "Negotiation Cancelled", "cancelledDescription": "The negotiation was cancelled due to declined proposals", "proposeTheyPayFirst": "Propose Other Pays First", "sendProposal": "Send Proposal", "newProposal": "{name} has made a new proposal", "finalOfferNotice": "This is your final offer", "final": "Final", "yourMessage": "Your Message", "addMessage": "Add Message", "says": "says", "theyRequestingToPaySecond": "They are requesting to pay second.", "explainYourRequest": "Explain your request", "counterMessagePlaceholder": "Counter offer message (optional)", "agreeToProposal": "Agree to Proposal", "cancel": "Cancel", "proceed": "Proceed", "reasons": {"lowerAmount": "Lower amount means lower risk", "trustScore": "Based on trust score analysis", "otherSuggested": "Other party suggested as first payer", "lowerReputation": "You have a lower reputation level ({level}) and should pay first", "lowerReputationOther": "{name} has a lower reputation level ({level}) and should pay first"}, "risk": {"low": "Low Risk", "medium": "Medium Risk", "high": "High Risk"}}, "confirmReceipt": {"title": "Confirm You Have Received {amount}", "warning": "⚠️ Only confirm if you have actually received the payment in your account.", "yourDetails": "Your receiving details:", "button": "Confirm Payment Received", "expectedWithin": "Expected within {hours} hours", "remaining": "Time remaining", "whenPaymentArrives": "When Payment Arrives", "addTrackingNumber": "Add tracking number", "trackingPlaceholder": "Enter tracking/reference number...", "uploadReceipt": "Upload receipt (optional)", "uploadHint": "Photo or screenshot", "chooseFile": "Choose <PERSON>", "fileInfo": "Max 5MB - JPG, PNG, PDF", "fileUploaded": "File uploaded: {name}", "invalidFileType": "Invalid file type. Please use JPG, PNG, or PDF.", "fileTooLarge": "File too large. Maximum size is 5MB.", "copyAllDetails": "Copy All Details", "notReceived": "Not Received Yet", "confirmed": "Receipt confirmed successfully!", "confirmFailed": "Failed to confirm receipt", "notReceivedReported": "Reported as not received", "reportFailed": "Failed to report not received", "paymentDeclared": "Payment Declared", "amountDeclared": "Amount Declared", "declaredBy": "Declared By", "trackingNumber": "Tracking Number", "reference": "Reference", "instructionTitle": "Confirm Payment Receipt", "instructionDescription": "Please confirm if you have received {amount} from {declaredBy} in your account. Only confirm if the payment has actually arrived.", "confirmationTitle": "Confirm Payment Receipt", "confirmationMessage": "Are you sure you have received {amount} {currency} from {declaredBy}? Only confirm if the payment is actually in your account.", "confirmButton": "Yes, I Received It", "cancelButton": "Cancel", "summaryText": "has declared they sent you", "declaredAt": "Declared at", "showDetails": "Show Details", "hideDetails": "Hide Details"}, "paymentDeclared": {"title": "Payment Sent: {amount}", "description": "You have declared that you sent {amount} to {recipient}. Waiting for them to confirm receipt.", "declarationDetails": "Payment Declaration Details", "amountSent": "Amount <PERSON>", "sentTo": "<PERSON><PERSON>", "trackingNumber": "Tracking Number", "reference": "Reference", "waitingTitle": "Waiting for {recipient} to Confirm", "waitingDescription": "{recipient} needs to check their account and confirm they received {amount}.", "nextSteps": "Next Steps", "step1": "Payment declared by you", "step2": "{recipient} confirms payment receipt", "step3": "Transaction continues to next phase", "contactSupport": "Contact Support", "cancelPayment": "Cancel Payment", "supportContacted": "Support has been notified. They will assist you shortly."}, "confirmFirstPaymentReceipt": {"title": "Confirm Final Payment Receipt for {amount}", "warning": "⚠️ Only confirm if you have actually received the payment in your account.", "yourDetails": "Your receiving details:", "button": "Confirm Payment Received", "expectedWithin": "Expected within {hours} hours", "remaining": "Time remaining", "whenPaymentArrives": "When Payment Arrives", "addTrackingNumber": "Add tracking number", "trackingPlaceholder": "Enter tracking/reference number...", "uploadReceipt": "Upload receipt (optional)", "uploadHint": "Photo or screenshot", "chooseFile": "Choose <PERSON>", "fileInfo": "Max 5MB - JPG, PNG, PDF", "fileUploaded": "File uploaded: {name}", "invalidFileType": "Invalid file type. Please use JPG, PNG, or PDF.", "fileTooLarge": "File too large. Maximum size is 5MB.", "copyAllDetails": "Copy All Details", "notReceived": "Not Received Yet", "confirmed": "Receipt confirmed successfully!", "confirmFailed": "Failed to confirm receipt", "notReceivedReported": "Reported as not received", "reportFailed": "Failed to report not received"}, "yourTurnToPay": {"title": "Your Turn: Send {amount} to {name}", "description": "Send the payment to the following details:", "copyButton": "Copy", "button": "I Have Sent The Payment", "instructions": "Payment Instructions", "importantNotes": "Important Notes", "estimatedTime": "Estimated time: {time}", "recipientDetails": "Recipient Payment Details", "sendAmount": "Amount to Send", "transitNumber": "Transit Number", "copyAllDetails": "Copy All Details", "afterSending": "After You Send the Payment", "referenceNumber": "Transaction Reference Number", "referencePlaceholder": "Enter tracking/reference number", "referenceHint": "This helps us track your payment and resolve any issues", "uploadProof": "Upload Payment Proof", "optional": "(Optional)", "chooseFile": "Choose <PERSON>", "fileInfo": "JPG, PNG, PDF (max 10MB)", "fileUploaded": "File uploaded: {name}", "invalidFileType": "Invalid file type. Please upload JPG, PNG, or PDF files only.", "fileTooLarge": "File is too large. Maximum size is 10MB.", "additionalNotes": "Additional Notes", "notesPlaceholder": "Any additional information about the payment...", "declared": "Payment declaration submitted successfully!", "declareFailed": "Failed to submit payment declaration. Please try again.", "step1": "Log into your online banking or visit your bank branch", "step2": "Select 'Send Money' or 'E-Transfer'", "step3": "Enter the recipient details provided above", "step4": "Complete the transfer and save the reference number", "waitingTitle": "Waiting for {name} to send payment", "waitingDescription": "Once {name} declares the payment, you will be notified to confirm receipt."}, "makeSecondPayment": {"title": "Your Turn: Send {amount} to {name}", "description": "Send the payment to the following details:", "copyButton": "Copy", "button": "I Have Sent The Payment", "instructions": "Payment Instructions", "importantNotes": "Important Notes", "estimatedTime": "Estimated time: {time}", "recipientDetails": "Recipient Payment Details", "sendAmount": "Amount to Send", "transitNumber": "Transit Number", "copyAllDetails": "Copy All Details", "afterSending": "After You Send the Payment", "referenceNumber": "Transaction Reference Number", "referencePlaceholder": "Enter tracking/reference number", "referenceHint": "This helps us track your payment and resolve any issues", "uploadProof": "Upload Payment Proof", "optional": "(Optional)", "chooseFile": "Choose <PERSON>", "fileInfo": "JPG, PNG, PDF (max 10MB)", "fileUploaded": "File uploaded: {name}", "invalidFileType": "Invalid file type. Please upload JPG, PNG, or PDF files only.", "fileTooLarge": "File is too large. Maximum size is 10MB.", "additionalNotes": "Additional Notes", "notesPlaceholder": "Any additional information about the payment...", "declared": "Payment declaration submitted successfully!", "declareFailed": "Failed to submit payment declaration. Please try again.", "step1": "Log into your online banking or visit your bank branch", "step2": "Select 'Send Money' or 'E-Transfer'", "step3": "Enter the recipient details provided above", "step4": "Complete the transfer and save the reference number", "waitingTitle": "Waiting for {name} to send payment", "waitingDescription": "Once {name} declares the payment, you will be notified to confirm receipt."}, "makePayment": {"title": "Your Turn: Send {amount} to {name}", "description": "Send the payment to the following details:", "copyButton": "Copy", "button": "I Have Sent The Payment", "instructions": "Payment Instructions", "importantNotes": "Important Notes", "estimatedTime": "Estimated time: {time}", "recipientDetails": "Recipient Payment Details", "sendAmount": "Amount to Send", "transitNumber": "Transit Number", "copyAllDetails": "Copy All Details", "afterSending": "After You Send the Payment", "referenceNumber": "Transaction Reference Number", "referencePlaceholder": "Enter tracking/reference number", "referenceHint": "This helps us track your payment and resolve any issues", "uploadProof": "Upload Payment Proof", "optional": "(Optional)", "chooseFile": "Choose <PERSON>", "fileInfo": "JPG, PNG, PDF (max 10MB)", "fileUploaded": "File uploaded: {name}", "invalidFileType": "Invalid file type. Please upload JPG, PNG, or PDF files only.", "fileTooLarge": "File is too large. Maximum size is 10MB.", "additionalNotes": "Additional Notes", "notesPlaceholder": "Any additional information about the payment...", "declared": "Payment declaration submitted successfully!", "declareFailed": "Failed to submit payment declaration. Please try again.", "step1": "Log into your online banking or visit your bank branch", "step2": "Select 'Send Money' or 'E-Transfer'", "step3": "Enter the recipient details provided above", "step4": "Complete the transfer and save the reference number", "waitingTitle": "Waiting for {name} to send payment", "waitingDescription": "Once {name} declares the payment, you will be notified to confirm receipt."}, "rateExperience": {"title": "Rate Your Experience with {name}", "description": "How was your trading experience?", "submitButton": "Submit Rating"}}, "chat": {"placeholder": "Type your message...", "send": "Send", "chatMode": "💬"}, "timer": {"minutes": "{count} minute | {count} minutes", "seconds": "{count} second | {count} seconds", "expired": "Time expired"}, "errors": {"loadingTransaction": "Failed to load transaction details", "sendingMessage": "Failed to send message", "performingAction": "Failed to perform action"}, "confirmations": {"sendPayment": "Are you sure you have sent the payment?", "confirmReceipt": "Are you sure you have received the payment?", "yes": "Yes", "no": "No"}, "payment": {"status": {"complete": "Verified", "pending": "Pending", "failed": "Failed"}, "bankName": "Bank Name", "accountNumber": "Account Number", "accountHolder": "Account Holder", "iban": "IBAN", "swiftCode": "SWIFT Code", "routingNumber": "Routing Number", "incompleteWarning": "Payment details are incomplete", "showDetails": "Show Details", "hideDetails": "Hide Details"}}